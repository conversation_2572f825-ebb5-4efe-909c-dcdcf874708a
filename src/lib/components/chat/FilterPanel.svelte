<script lang="ts">
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';
	import { Button, Checkbox, Datepicker } from 'flowbite-svelte';
	import {
		CalendarMonthOutline,
		CloseOutline,
		CheckOutline,
		BullhornOutline,
		FilterOutline
	} from 'flowbite-svelte-icons';
	import { t } from '$lib/stores/i18n';

	export let isOpen = false;
	export let sortedIdentities = [];
	export let unreadCounts = new Map();
	export let filterData = {
		dateRange: null,
		customStartDate: null,
		customEndDate: null,
		platforms: new Set(['All']),
		channels: new Set(['All']),
		unreadFilter: new Set(['All']),
		statuses: new Set(['All']),
		tags: new Set(['All']),
		owners: new Set(['All']),
		searchText: ''
	};

	const dispatch = createEventDispatcher();

	// Filter options
	const dateRangeOptions = [
		{ id: 'today', label: t('chat_center_filter_daterange_today') },
		{ id: 'yesterday', label: t('chat_center_filter_daterange_yesterday') },
		{ id: 'week', label: t('chat_center_filter_daterange_week') },
		{ id: 'month', label: t('chat_center_filter_daterange_1month') },
		{ id: 'last-3-months', label: t('chat_center_filter_daterange_3months') },
		{ id: 'last-6-months', label: t('chat_center_filter_daterange_6months') }
		// { id: 'custom', label: 'Custom Range' }
	];

	const unreadFilterOptions = [
		{ id: 'All', label: t('chat_center_all_messages') },
		{ id: 'unread', label: t('chat_center_unread_messages') }
	];

	// Status filter options
	const statusFilterOptions = [
		{ id: 'All', label: t('chat_center_filter_status_all') },
		{ id: 'open', label: 'Open' },
		{ id: 'waiting', label: 'Waiting' },
		{ id: 'assigned', label: 'Assigned' },
		{ id: 'pending-to-close', label: 'Pending to Close' },
		{ id: 'closed', label: 'Closed' }
	];

	// Tag filter options
	const tagFilterOptions = [
		{ id: 'All', label: t('chat_center_filter_tag_all') },
		{ id: 'vip', label: 'VIP' },
		{ id: 'vvip', label: 'VVIP' }
	];

	// Owner filter options - dynamically extracted from identities
	$: ownerFilterOptions = [
		{ id: 'All', label: t('chat_center_filter_owner_all') || 'All Owners' },
		...sortedIdentities
			.map((identity) => {
				const fullOwner = (identity as any)['latest_ticket_owner'];
				if (!fullOwner) return null;

				// Extract first word/name only
				const ownerFirstName = fullOwner.split(/\s+/)[0];
				return {
					id: ownerFirstName,
					label: ownerFirstName
				};
			})
			.filter((option) => option !== null) // Remove null entries
			.filter(
				(option, index, self) =>
					// Remove duplicates based on owner name
					index === self.findIndex((o) => o.id === option.id)
			)
	];

	// Combined platform+channel filter options
	$: combinedPlatformChannelOptions = [
		{
			id: 'All',
			label: t('chat_center_filter_channel_all'),
			platform: 'All',
			channel: 'All',
			color: 'bg-gray-100 text-gray-700'
		},
		...sortedIdentities
			.map((identity) => {
				const platform = identity.platform;
				const channel = identity.channel_name || 'No Channel';
				const combinedId = `${platform}|${channel}`;
				const platformColor =
					platform === 'LINE'
						? 'bg-green-100 text-green-700'
						: platform === 'WHATSAPP'
							? 'bg-green-100 text-green-700'
							: platform === 'FACEBOOK'
								? 'bg-blue-100 text-blue-700'
								: platform === 'INSTAGRAM'
									? 'bg-purple-100 text-purple-700'
									: platform === 'TELEGRAM'
										? 'bg-sky-100 text-sky-700'
										: 'bg-gray-100 text-gray-700';

				return {
					id: combinedId,
					label: channel,
					platform: platform,
					channel: channel,
					color: platformColor
				};
			})
			.filter(
				(option, index, self) =>
					// Remove duplicates based on combined id
					index === self.findIndex((o) => o.id === option.id)
			)
	];

	let filterPanelElement: HTMLElement;

	// Local state for form inputs
	let selectedDateRange = filterData.dateRange;
	let customStartDate = filterData.customStartDate;
	let customEndDate = filterData.customEndDate;
	let selectedPlatforms = new Set(filterData.platforms);
	let selectedChannels = new Set(filterData.channels);
	let selectedUnreadFilter = new Set(filterData.unreadFilter);
	let selectedStatuses = new Set(filterData.statuses);
	let selectedTags = new Set(filterData.tags);
	let selectedOwners = new Set(filterData.owners);
	let selectedCombinedPlatformChannel = new Set(['All']); // New combined filter state

	$: showCustomDateInputs = selectedDateRange === 'custom';

	// Handle click outside to close
	const handleClickOutside = (event: MouseEvent) => {
		console.log('Click outside detected, isOpen:', isOpen);
		if (isOpen && filterPanelElement && !filterPanelElement.contains(event.target as Node)) {
			// Also check if the click is on the filter button itself (to prevent immediate closing)
			const target = event.target as Element;
			const filterButton = target.closest('[aria-label="Filter conversations"]');
			console.log('Filter button found:', !!filterButton);
			if (!filterButton) {
				console.log('Closing panel due to click outside');
				closePanel();
			}
		}
	};

	// Handle escape key to close
	const handleKeydown = (event: KeyboardEvent) => {
		if (event.key === 'Escape' && isOpen) {
			closePanel();
		}
	};

	// Track if event listeners are attached
	let listenersAttached = false;

	// Reactive statement to manage event listeners based on isOpen state
	$: if (typeof document !== 'undefined') {
		if (isOpen && !listenersAttached) {
			// Add a small delay to prevent immediate closing from the same click that opened the panel
			setTimeout(() => {
				console.log('Adding event listeners');
				document.addEventListener('click', handleClickOutside);
				document.addEventListener('keydown', handleKeydown);
				listenersAttached = true;
			}, 50);
		} else if (!isOpen && listenersAttached) {
			console.log('Removing event listeners');
			document.removeEventListener('click', handleClickOutside);
			document.removeEventListener('keydown', handleKeydown);
			listenersAttached = false;
		}
	}

	onMount(() => {
		return () => {
			// Cleanup on component destroy
			if (typeof document !== 'undefined') {
				document.removeEventListener('click', handleClickOutside);
				document.removeEventListener('keydown', handleKeydown);
			}
		};
	});

	function closePanel() {
		isOpen = false;
		dispatch('close');
	}

	function clearAllFilters() {
		selectedDateRange = null;
		customStartDate = null;
		customEndDate = null;
		selectedPlatforms = new Set(['All']);
		selectedChannels = new Set(['All']);
		selectedUnreadFilter = new Set(['All']);
		selectedStatuses = new Set(['All']);
		selectedTags = new Set(['All']);
		selectedOwners = new Set(['All']);
		selectedCombinedPlatformChannel = new Set(['All']);
		applyFilters();
	}

	function applyFilters() {
		// Convert combined filter back to separate platform and channel filters for compatibility
		const platforms = new Set();
		const channels = new Set();

		if (selectedCombinedPlatformChannel.has('All')) {
			platforms.add('All');
			channels.add('All');
		} else {
			selectedCombinedPlatformChannel.forEach((combinedId) => {
				if (combinedId !== 'All') {
					const [platform, channel] = combinedId.split('|');
					platforms.add(platform);
					channels.add(channel);
				}
			});

			// If no specific selections, default to All
			if (platforms.size === 0) platforms.add('All');
			if (channels.size === 0) channels.add('All');
		}

		const updatedFilterData = {
			dateRange: selectedDateRange,
			customStartDate,
			customEndDate,
			platforms: platforms,
			channels: channels,
			unreadFilter: selectedUnreadFilter,
			statuses: selectedStatuses,
			tags: selectedTags,
			owners: selectedOwners,
			searchText: '' // Remove search functionality
		};

		dispatch('apply', updatedFilterData);
	}

	function toggleUnreadFilter(filterId: string) {
		if (filterId === 'All') {
			selectedUnreadFilter = new Set(['All']);
		} else {
			selectedUnreadFilter.delete('All');
			if (selectedUnreadFilter.has(filterId)) {
				selectedUnreadFilter.delete(filterId);
			} else {
				selectedUnreadFilter.add(filterId);
			}

			// If no filters selected, select All
			if (selectedUnreadFilter.size === 0) {
				selectedUnreadFilter.add('All');
			}
		}
		selectedUnreadFilter = selectedUnreadFilter; // Trigger reactivity
	}

	function toggleCombinedPlatformChannel(combinedId: string) {
		if (combinedId === 'All') {
			selectedCombinedPlatformChannel = new Set(['All']);
		} else {
			selectedCombinedPlatformChannel.delete('All');
			if (selectedCombinedPlatformChannel.has(combinedId)) {
				selectedCombinedPlatformChannel.delete(combinedId);
			} else {
				selectedCombinedPlatformChannel.add(combinedId);
			}

			// If no filters selected, select All
			if (selectedCombinedPlatformChannel.size === 0) {
				selectedCombinedPlatformChannel.add('All');
			}
		}
		selectedCombinedPlatformChannel = selectedCombinedPlatformChannel; // Trigger reactivity
	}

	function toggleStatusFilter(statusId: string) {
		if (statusId === 'All') {
			selectedStatuses = new Set(['All']);
		} else {
			selectedStatuses.delete('All');
			if (selectedStatuses.has(statusId)) {
				selectedStatuses.delete(statusId);
			} else {
				selectedStatuses.add(statusId);
			}

			// If no filters selected, select All
			if (selectedStatuses.size === 0) {
				selectedStatuses.add('All');
			}
		}
		selectedStatuses = selectedStatuses; // Trigger reactivity
	}

	function toggleTagFilter(tagId: string) {
		if (tagId === 'All') {
			selectedTags = new Set(['All']);
		} else {
			selectedTags.delete('All');
			if (selectedTags.has(tagId)) {
				selectedTags.delete(tagId);
			} else {
				selectedTags.add(tagId);
			}

			// If no filters selected, select All
			if (selectedTags.size === 0) {
				selectedTags.add('All');
			}
		}
		selectedTags = selectedTags; // Trigger reactivity
	}

	function toggleOwnerFilter(ownerId: string) {
		if (ownerId === 'All') {
			selectedOwners = new Set(['All']);
		} else {
			selectedOwners.delete('All');
			if (selectedOwners.has(ownerId)) {
				selectedOwners.delete(ownerId);
			} else {
				selectedOwners.add(ownerId);
			}

			// If no filters selected, select All
			if (selectedOwners.size === 0) {
				selectedOwners.add('All');
			}
		}
		selectedOwners = selectedOwners; // Trigger reactivity
	}

	function getPlatformIcon(platform: string): string {
		const icons = {
			LINE: '/images/platform-line.png',
			WHATSAPP: '/images/platform-whatsapp.png',
			FACEBOOK: '/images/platform-facebook.png',
			INSTAGRAM: '/images/platform-instagram.png'
		};
		return icons[platform] || '/images/platform-line.png';
	}

	// Count active filters
	$: activeFiltersCount = [
		selectedDateRange ? 1 : 0,
		selectedCombinedPlatformChannel.size > 1 || !selectedCombinedPlatformChannel.has('All') ? 1 : 0,
		selectedUnreadFilter.size > 1 || !selectedUnreadFilter.has('All') ? 1 : 0,
		selectedStatuses.size > 1 || !selectedStatuses.has('All') ? 1 : 0,
		selectedTags.size > 1 || !selectedTags.has('All') ? 1 : 0,
		selectedOwners.size > 1 || !selectedOwners.has('All') ? 1 : 0
	].reduce((sum, count) => sum + count, 0);
</script>

{#if isOpen}
	<div
		bind:this={filterPanelElement}
		class="absolute left-0 top-full z-50 mt-2 min-w-[700px] rounded-lg border border-gray-200 bg-white p-6 shadow-lg"
		role="dialog"
		aria-label="Filter conversations"
		aria-modal="true"
	>
		<!-- Header -->
		<div class="mb-6 flex items-center justify-between">
			<h3 class="text-lg font-semibold text-gray-900">{t('filter')}</h3>
			<div class="flex items-center gap-2">
				<!-- {#if activeFiltersCount > 0}
					<span class="text-sm text-gray-500">
						{activeFiltersCount} active
					</span>
				{/if} -->
				<button
					type="button"
					on:click={closePanel}
					class="rounded-lg p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
					aria-label="Close filter panel"
				>
					<CloseOutline class="h-5 w-5" />
				</button>
			</div>
		</div>

		<!-- Unread Messages Filter - Top Section -->
		<div class="mb-6">
			<!-- <div class="mb-3 text-sm font-medium text-gray-700">Unread Messages</div> -->
			<div class="flex flex-wrap gap-2">
				{#each unreadFilterOptions as filter}
					<div
						class="flex cursor-pointer items-center rounded-lg border border-gray-200 px-3 py-2 transition-colors hover:bg-gray-50 {selectedUnreadFilter.has(
							filter.id
						)
							? 'border-blue-200 bg-blue-50'
							: ''}"
					>
						<Checkbox
							checked={selectedUnreadFilter.has(filter.id)}
							on:change={() => {
								toggleUnreadFilter(filter.id);
								applyFilters();
							}}
							class="mr-2 text-sm text-blue-600 focus:ring-blue-500"
						/>
						<span class="text-sm text-gray-700">{filter.label}</span>
					</div>
				{/each}
			</div>
		</div>

		<!-- Horizontal Filter Row -->
		<div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-12">
			<!-- Date Range Filter -->
			<div class="lg:col-span-3">
				<div class="mb-2 text-sm font-medium text-gray-700">
					<!-- <CalendarMonthOutline class="mr-1 inline h-4 w-4" /> -->
					{t('chat_center_filter_daterange')}
				</div>
				<div class="h-30 space-y-1 overflow-y-auto">
					{#each dateRangeOptions as option}
						<label class="flex items-center text-sm">
							<input
								type="radio"
								bind:group={selectedDateRange}
								value={option.id}
								on:change={applyFilters}
								class="my-1 ml-1 mr-2 scale-90 text-blue-600 focus:ring-blue-500"
							/>
							<span class="text-gray-700">{option.label}</span>
						</label>
					{/each}
				</div>

				<!-- {#if showCustomDateInputs}
					<div class="mt-2 space-y-1">
						<div class="datepicker-right relative">
							<div class="block text-sm text-gray-600">Start</div>
							<div class="relative">
								<Datepicker
									bind:value={customStartDate}
									placeholder="Start date"
									on:change={applyFilters}
								/>
							</div>
						</div>
						<div class="datepicker-right relative">
							<div class="block text-sm text-gray-600">End</div>
							<div class="relative">
								<Datepicker
									bind:value={customEndDate}
									placeholder="End date"
									on:change={applyFilters}
								/>
							</div>
						</div>
					</div>
				{/if} -->
			</div>

			<!-- Owner Filter -->
			<div class="lg:col-span-3">
				<div class="mb-2 text-sm font-medium text-gray-700">{t('chat_center_filter_owner')}</div>
				<div class="h-30 space-y-1 overflow-y-auto">
					{#each ownerFilterOptions as option}
						<div class="flex items-center rounded px-2 py-1 text-sm hover:bg-gray-50">
							<Checkbox
								checked={selectedOwners.has(option.id)}
								on:change={() => {
									toggleOwnerFilter(option.id);
									applyFilters();
								}}
								class="mr-1 scale-100 text-blue-600 focus:ring-blue-500"
							/>
							<span class="text-gray-700">{option.label}</span>
						</div>
					{/each}
				</div>
			</div>

			<!-- Tag Filter -->
			<!-- <div class="lg:col-span-3">
				<div class="mb-2 text-sm font-medium text-gray-700">{t('chat_center_filter_tag')}</div>
				<div class="h-30 space-y-1 overflow-y-auto">
					{#each tagFilterOptions as option}
						<div class="flex items-center rounded px-2 py-1 text-sm hover:bg-gray-50">
							<Checkbox
								checked={selectedTags.has(option.id)}
								on:change={() => {
									toggleTagFilter(option.id);
									applyFilters();
								}}
								class="mr-1 scale-100 text-blue-600 focus:ring-blue-500"
							/>
							<span class="text-gray-700">{option.label}</span>
						</div>
					{/each}
				</div>
			</div> -->

			<!-- Status Filter -->
			<!-- <div class="lg:col-span-3">
				<div class="mb-2 text-sm font-medium text-gray-700">{t('chat_center_filter_status')}</div>
				<div class="h-30 space-y-1 overflow-y-auto">
					{#each statusFilterOptions as option}
						<div class="flex items-center rounded px-2 py-1 text-sm hover:bg-gray-50">
							<Checkbox
								checked={selectedStatuses.has(option.id)}
								on:change={() => {
									toggleStatusFilter(option.id);
									applyFilters();
								}}
								class="mr-1 scale-100 text-blue-600 focus:ring-blue-500"
							/>
							<span class="text-gray-700">{option.label}</span>
						</div>
					{/each}
				</div>
			</div> -->

			<!-- Combined Platform+Channel Filter -->
			<div class="lg:col-span-6">
				<div class="mb-2 text-sm font-medium text-gray-700">{t('chat_center_filter_channel')}</div>
				<div class="h-30 space-y-1 overflow-y-auto">
					{#each combinedPlatformChannelOptions as option}
						<div class="flex items-center rounded px-2 py-1 text-sm hover:bg-gray-50">
							<Checkbox
								checked={selectedCombinedPlatformChannel.has(option.id)}
								on:change={() => {
									toggleCombinedPlatformChannel(option.id);
									applyFilters();
								}}
								class="mr-1 scale-100 text-blue-600 focus:ring-blue-500"
							/>
							<div class="flex flex-1 items-center gap-1">
								{#if option.platform !== 'All'}
									<img
										src={getPlatformIcon(option.platform)}
										alt={option.platform}
										class="h-4 w-4 rounded-full object-cover"
									/>
								{/if}
								<span class="truncate text-gray-700">{option.label}</span>
							</div>
						</div>
					{/each}
				</div>
			</div>
		</div>

		<!-- Actions -->
		<div class="flex justify-between border-t border-gray-200 pt-4">
			<Button
				color="none"
				class="text-gray-600 hover:bg-gray-100"
				on:click={clearAllFilters}
				disabled={activeFiltersCount === 0}
			>
				{t('chat_center_clear_button')}
			</Button>
			<Button color="blue" on:click={closePanel}><CheckOutline class="mr-2 h-4 w-4" />{t('chat_center_filter_button')}</Button>
		</div>
	</div>
{/if}

<style>
	/* Position datepicker calendar popup to the right */
	.datepicker-right {
		position: relative;
	}

	/* Target various possible calendar popup selectors used by Flowbite Datepicker */
	:global(.datepicker-right .datepicker-picker),
	:global(.datepicker-right .datepicker-dropdown),
	:global(.datepicker-right [data-popper-placement]),
	:global(.datepicker-right .absolute[role='dialog']),
	:global(.datepicker-right .absolute.z-50) {
		left: auto !important;
		right: 0 !important;
		transform: translateX(0) !important;
		min-width: 280px;
		z-index: 1000;
	}

	/* Target calendar popup that might be positioned relative to input */
	:global(.datepicker-right .relative .absolute),
	:global(.datepicker-right > .relative > .absolute) {
		left: auto !important;
		right: 0 !important;
		transform: translateX(0) !important;
	}

	/* Alternative approach: target by common Flowbite calendar classes */
	:global(.datepicker-right [class*='calendar']),
	:global(.datepicker-right [class*='picker']),
	:global(.datepicker-right [class*='dropdown']) {
		left: auto !important;
		right: 0 !important;
		transform: translateX(0) !important;
	}

	/* Ensure calendar doesn't get cut off at the edge */
	:global(.datepicker-right .absolute) {
		max-width: calc(100vw - 20px);
	}

	/* Override any transform that might center the calendar */
	:global(.datepicker-right [style*='transform']) {
		transform: translateX(0) !important;
	}
</style>
